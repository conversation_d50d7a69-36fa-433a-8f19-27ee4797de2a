/**
 * Simple email test script
 * Tests email deliverability using the configured email service
 */

import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'

// Load environment variables from .env.production
try {
  const envContent = readFileSync('.env.production', 'utf8')
  const envVars = {}
  envContent.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=')
    if (key && valueParts.length > 0) {
      const value = valueParts.join('=').replace(/^"(.*)"$/, '$1')
      envVars[key.trim()] = value.trim()
    }
  })
  Object.assign(process.env, envVars)
} catch (error) {
  console.log('⚠️  Could not load .env.production file:', error.message)
}

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://kmejequnwwngmzwkszqs.supabase.co'
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImttZWplcXVud3duZ216d2tzenFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyMDk3ODQsImV4cCI6MjA2Mzc4NTc4NH0.ROGVGPdlDh_o1TJJJijM1BTubWUhUXUh5oZWxOKDdjw'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function testEmailDeliverability() {
  console.log('🧪 Testing email deliverability...')
  
  try {
    // Test 1: Configuration Test
    console.log('\n1. Testing email configuration...')
    
    // Check if email service function exists
    const { data: functions, error: functionsError } = await supabase
      .from('pg_proc')
      .select('proname')
      .eq('proname', 'get_rate_limit_config')
      .limit(1)
    
    if (functionsError) {
      console.log('❌ Database functions check failed:', functionsError.message)
    } else {
      console.log('✅ Database functions are available')
    }

    // Test 2: Send Test Email
    console.log('\n2. Sending test email...')
    
    const testEmailData = {
      type: 'test',
      to: '<EMAIL>', // Replace with your test email
      data: {
        test_message: 'Email deliverability test from KAYA Finance',
        timestamp: new Date().toISOString(),
        webhook_secret_configured: process.env.EMAIL_BOUNCE_WEBHOOK_SECRET ? 'Yes' : 'No'
      }
    }

    const { data: emailResult, error: emailError } = await supabase.functions.invoke('email-service', {
      body: testEmailData
    })

    if (emailError) {
      console.log('❌ Email sending failed:', emailError.message)
      console.log('Error details:', emailError)
    } else {
      console.log('✅ Test email sent successfully!')
      console.log('Email result:', emailResult)
    }

    // Test 3: Check Rate Limiting Configuration
    console.log('\n3. Testing rate limiting configuration...')
    
    // Get a sample organization ID
    const { data: orgs, error: orgsError } = await supabase
      .from('organizations')
      .select('id')
      .limit(1)

    if (orgsError) {
      console.log('❌ Could not fetch organization:', orgsError.message)
    } else if (orgs && orgs.length > 0) {
      const orgId = orgs[0].id
      
      const { data: rateLimitConfig, error: rateLimitError } = await supabase.rpc('get_rate_limit_config', {
        org_id_param: orgId
      })

      if (rateLimitError) {
        console.log('❌ Rate limit configuration test failed:', rateLimitError.message)
      } else {
        console.log('✅ Rate limiting is configured:')
        console.log('  - Per minute:', rateLimitConfig.per_minute)
        console.log('  - Per hour:', rateLimitConfig.per_hour)
        console.log('  - Per day:', rateLimitConfig.per_day)
        console.log('  - Burst limit:', rateLimitConfig.burst_limit)
      }
    }

    // Test 4: Check Email Tables
    console.log('\n4. Checking email system tables...')
    
    const tables = [
      'email_deliveries',
      'email_bounce_events', 
      'email_suppressions',
      'email_retry_queue',
      'email_rate_limit_configs',
      'email_sending_stats'
    ]

    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('id')
          .limit(1)

        if (error && error.code === '42P01') {
          console.log(`❌ Table ${table} does not exist`)
        } else if (error) {
          console.log(`⚠️  Table ${table} exists but has issues:`, error.message)
        } else {
          console.log(`✅ Table ${table} is accessible`)
        }
      } catch (err) {
        console.log(`❌ Error checking table ${table}:`, err.message)
      }
    }

    // Test 5: Environment Variables Check
    console.log('\n5. Checking environment variables...')
    
    const envVars = [
      'RESEND_API_KEY',
      'EMAIL_BOUNCE_WEBHOOK_SECRET',
      'VITE_FROM_EMAIL',
      'EMAIL_RATE_LIMIT_PER_HOUR'
    ]

    envVars.forEach(envVar => {
      const value = process.env[envVar]
      if (value) {
        console.log(`✅ ${envVar} is configured`)
      } else {
        console.log(`❌ ${envVar} is not set`)
      }
    })

    console.log('\n📊 Email deliverability test completed!')
    console.log('\nNext steps:')
    console.log('1. Check your email inbox for the test email')
    console.log('2. Verify webhook endpoint is accessible')
    console.log('3. Monitor email delivery logs in the dashboard')

  } catch (error) {
    console.error('❌ Test failed with error:', error)
  }
}

// Run the test
testEmailDeliverability()
  .then(() => {
    console.log('\n✅ Test script completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n❌ Test script failed:', error)
    process.exit(1)
  })

export { testEmailDeliverability }
