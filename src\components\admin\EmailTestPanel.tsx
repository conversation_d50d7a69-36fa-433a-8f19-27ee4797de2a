/**
 * Email Test Panel
 * Component for testing email deliverability and configuration
 */

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/use-toast'
import { 
  runEmailDeliverabilityTests, 
  quickEmailTest,
  type EmailDeliverabilityReport 
} from '@/utils/testEmailDeliverability'
import { 
  Mail, 
  Send, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw,
  TestTube,
  Settings
} from 'lucide-react'

export function EmailTestPanel() {
  const [testEmail, setTestEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [quickTesting, setQuickTesting] = useState(false)
  const [testReport, setTestReport] = useState<EmailDeliverabilityReport | null>(null)
  const { toast } = useToast()

  const handleQuickTest = async () => {
    if (!testEmail) {
      toast({
        title: 'Error',
        description: 'Please enter a test email address',
        variant: 'destructive'
      })
      return
    }

    setQuickTesting(true)
    try {
      const success = await quickEmailTest(testEmail)
      
      toast({
        title: success ? 'Success' : 'Failed',
        description: success 
          ? 'Test email sent successfully! Check your inbox.'
          : 'Failed to send test email. Check configuration.',
        variant: success ? 'default' : 'destructive'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send test email',
        variant: 'destructive'
      })
    } finally {
      setQuickTesting(false)
    }
  }

  const handleFullTest = async () => {
    setLoading(true)
    try {
      const report = await runEmailDeliverabilityTests(testEmail || '<EMAIL>')
      setTestReport(report)
      
      toast({
        title: 'Test Complete',
        description: `Email deliverability score: ${report.overall.score}%`,
        variant: report.overall.success ? 'default' : 'destructive'
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to run deliverability tests',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    )
  }

  const getStatusBadge = (success: boolean) => {
    return (
      <Badge variant={success ? "default" : "destructive"}>
        {success ? "Pass" : "Fail"}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Email Deliverability Testing
          </CardTitle>
          <CardDescription>
            Test your email configuration and deliverability to ensure emails are working correctly.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="test-email">Test Email Address</Label>
            <Input
              id="test-email"
              type="email"
              placeholder="<EMAIL>"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
            />
            <p className="text-sm text-muted-foreground">
              Enter an email address where you can receive test emails
            </p>
          </div>

          <div className="flex gap-2">
            <Button 
              onClick={handleQuickTest}
              disabled={quickTesting || !testEmail}
              variant="outline"
            >
              {quickTesting ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Quick Test
            </Button>
            
            <Button 
              onClick={handleFullTest}
              disabled={loading}
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <TestTube className="h-4 w-4 mr-2" />
              )}
              Full Deliverability Test
            </Button>
          </div>

          {testReport && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Email deliverability score: <strong>{testReport.overall.score}%</strong>
                {testReport.overall.success ? ' - System is ready for production' : ' - Issues need attention'}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {testReport && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
            <CardDescription>
              Detailed results from the email deliverability tests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="overview" className="space-y-4">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>Overall Score</span>
                    <div className="flex items-center gap-2">
                      <Progress value={testReport.overall.score} className="w-24" />
                      <span className="text-sm font-medium">{testReport.overall.score}%</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(testReport.configurationTest.success)}
                        <span className="text-sm">Configuration</span>
                      </div>
                      {getStatusBadge(testReport.configurationTest.success)}
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(testReport.templateTest.success)}
                        <span className="text-sm">Templates</span>
                      </div>
                      {getStatusBadge(testReport.templateTest.success)}
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(testReport.deliveryTest.success)}
                        <span className="text-sm">Delivery</span>
                      </div>
                      {getStatusBadge(testReport.deliveryTest.success)}
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(testReport.webhookTest.success)}
                        <span className="text-sm">Webhooks</span>
                      </div>
                      {getStatusBadge(testReport.webhookTest.success)}
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(testReport.rateLimitTest.success)}
                        <span className="text-sm">Rate Limiting</span>
                      </div>
                      {getStatusBadge(testReport.rateLimitTest.success)}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="details" className="space-y-4">
                <div className="space-y-4">
                  {[
                    { name: 'Configuration Test', result: testReport.configurationTest },
                    { name: 'Template Test', result: testReport.templateTest },
                    { name: 'Delivery Test', result: testReport.deliveryTest },
                    { name: 'Webhook Test', result: testReport.webhookTest },
                    { name: 'Rate Limit Test', result: testReport.rateLimitTest }
                  ].map(({ name, result }) => (
                    <div key={name} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{name}</h4>
                        {getStatusBadge(result.success)}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{result.message}</p>
                      <p className="text-xs text-muted-foreground">
                        Tested at: {new Date(result.timestamp).toLocaleString()}
                      </p>
                      {result.details && (
                        <details className="mt-2">
                          <summary className="text-xs cursor-pointer text-blue-600">
                            View Details
                          </summary>
                          <pre className="text-xs mt-2 p-2 bg-gray-50 rounded overflow-auto">
                            {JSON.stringify(result.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="recommendations" className="space-y-4">
                <div className="space-y-3">
                  {testReport.overall.recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start gap-2 p-3 border rounded-lg">
                      <AlertTriangle className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{recommendation}</span>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Email Configuration Status
          </CardTitle>
          <CardDescription>
            Current email system configuration overview
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Webhook Secret</Label>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {process.env.EMAIL_BOUNCE_WEBHOOK_SECRET ? 'Configured' : 'Not Set'}
                </Badge>
                {process.env.EMAIL_BOUNCE_WEBHOOK_SECRET && (
                  <span className="text-xs text-muted-foreground">
                    whsec_***{process.env.EMAIL_BOUNCE_WEBHOOK_SECRET.slice(-8)}
                  </span>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Email Provider</Label>
              <Badge variant="outline">
                {process.env.VITE_EMAIL_PROVIDER || 'resend'}
              </Badge>
            </div>

            <div className="space-y-2">
              <Label>From Email</Label>
              <Badge variant="outline">
                {process.env.VITE_FROM_EMAIL || 'Not Set'}
              </Badge>
            </div>

            <div className="space-y-2">
              <Label>Rate Limiting</Label>
              <Badge variant="outline">
                {process.env.EMAIL_RATE_LIMIT_PER_HOUR || '100'}/hour
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
