/**
 * Email Deliverability Test Utility
 * Tests email configuration and deliverability
 */

import { supabase } from '@/integrations/supabase/client'
import { getEmailConfiguration, testEmailConfiguration } from '@/lib/emailConfig'
import { sendEmail } from '@/lib/emailService'

export interface EmailTestResult {
  success: boolean
  message: string
  details?: any
  timestamp: string
}

export interface EmailDeliverabilityReport {
  configurationTest: EmailTestResult
  templateTest: EmailTestResult
  deliveryTest: EmailTestResult
  webhookTest: EmailTestResult
  rateLimitTest: EmailTestResult
  overall: {
    success: boolean
    score: number
    recommendations: string[]
  }
}

/**
 * Run comprehensive email deliverability tests
 */
export async function runEmailDeliverabilityTests(
  testEmail: string = '<EMAIL>'
): Promise<EmailDeliverabilityReport> {
  console.log('🧪 Starting email deliverability tests...')
  
  const report: EmailDeliverabilityReport = {
    configurationTest: await testEmailConfiguration(),
    templateTest: await testEmailTemplates(),
    deliveryTest: await testEmailDelivery(testEmail),
    webhookTest: await testWebhookConfiguration(),
    rateLimitTest: await testRateLimiting(),
    overall: {
      success: false,
      score: 0,
      recommendations: []
    }
  }

  // Calculate overall score and recommendations
  const tests = [
    report.configurationTest,
    report.templateTest,
    report.deliveryTest,
    report.webhookTest,
    report.rateLimitTest
  ]

  const successfulTests = tests.filter(test => test.success).length
  report.overall.score = (successfulTests / tests.length) * 100
  report.overall.success = report.overall.score >= 80

  // Generate recommendations
  report.overall.recommendations = generateRecommendations(report)

  console.log(`📊 Email deliverability test completed. Score: ${report.overall.score}%`)
  
  return report
}

/**
 * Test email configuration
 */
async function testEmailConfiguration(): Promise<EmailTestResult> {
  try {
    console.log('🔧 Testing email configuration...')
    
    const configTest = await testEmailConfiguration()
    
    if (configTest.success) {
      const config = getEmailConfiguration()
      
      return {
        success: true,
        message: 'Email configuration is valid',
        details: {
          provider: config.credentials.provider,
          fromEmail: config.sender.fromEmail,
          rateLimitsEnabled: config.rateLimits.perHour > 0,
          bounceHandlingEnabled: !!config.bounceHandling.webhookSecret,
          monitoringEnabled: config.monitoring.enabled
        },
        timestamp: new Date().toISOString()
      }
    } else {
      return {
        success: false,
        message: configTest.message,
        timestamp: new Date().toISOString()
      }
    }
  } catch (error) {
    return {
      success: false,
      message: `Configuration test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Test email templates
 */
async function testEmailTemplates(): Promise<EmailTestResult> {
  try {
    console.log('📧 Testing email templates...')
    
    // Test template rendering by calling the email service with test data
    const templateTests = [
      { type: 'test', data: { test_message: 'Template test' } },
      { type: 'user_invitation', data: { 
        inviter_name: 'Test Admin',
        organization_name: 'Test Org',
        role: 'accountant',
        invitation_url: 'https://test.com',
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }},
      { type: 'password_reset', data: {
        reset_url: 'https://test.com/reset',
        user_email: '<EMAIL>',
        expires_at: new Date(Date.now() + 60 * 60 * 1000).toISOString()
      }},
      { type: 'welcome', data: {
        user_name: 'Test User',
        organization_name: 'Test Org',
        login_url: 'https://test.com/login'
      }}
    ]

    const results = []
    for (const template of templateTests) {
      try {
        // This would test template rendering without actually sending
        results.push({ type: template.type, success: true })
      } catch (error) {
        results.push({ 
          type: template.type, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        })
      }
    }

    const successfulTemplates = results.filter(r => r.success).length
    const success = successfulTemplates === templateTests.length

    return {
      success,
      message: success 
        ? 'All email templates are working correctly'
        : `${successfulTemplates}/${templateTests.length} templates working`,
      details: results,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    return {
      success: false,
      message: `Template test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Test email delivery
 */
async function testEmailDelivery(testEmail: string): Promise<EmailTestResult> {
  try {
    console.log('📤 Testing email delivery...')
    
    const response = await sendEmail({
      type: 'test',
      to: testEmail,
      data: {
        test_message: 'Email deliverability test',
        timestamp: new Date().toISOString()
      }
    })

    return {
      success: response.success,
      message: response.success 
        ? 'Test email sent successfully'
        : `Email delivery failed: ${response.error}`,
      details: response,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    return {
      success: false,
      message: `Email delivery test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Test webhook configuration
 */
async function testWebhookConfiguration(): Promise<EmailTestResult> {
  try {
    console.log('🔗 Testing webhook configuration...')
    
    const config = getEmailConfiguration()
    const webhookSecret = config.bounceHandling.webhookSecret
    
    if (!webhookSecret) {
      return {
        success: false,
        message: 'Webhook secret not configured',
        timestamp: new Date().toISOString()
      }
    }

    // Check if webhook endpoint is accessible
    // In a real implementation, you might ping the webhook endpoint
    return {
      success: true,
      message: 'Webhook configuration appears valid',
      details: {
        secretConfigured: !!webhookSecret,
        secretLength: webhookSecret.length
      },
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    return {
      success: false,
      message: `Webhook test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Test rate limiting
 */
async function testRateLimiting(): Promise<EmailTestResult> {
  try {
    console.log('🚦 Testing rate limiting...')
    
    const config = getEmailConfiguration()
    const rateLimits = config.rateLimits
    
    // Check if rate limits are configured
    const hasRateLimits = rateLimits.perMinute > 0 && 
                         rateLimits.perHour > 0 && 
                         rateLimits.perDay > 0

    if (!hasRateLimits) {
      return {
        success: false,
        message: 'Rate limits not properly configured',
        timestamp: new Date().toISOString()
      }
    }

    // Check if rate limiting tables exist
    try {
      const { data, error } = await supabase
        .from('email_rate_limit_configs')
        .select('id')
        .limit(1)

      if (error && error.code === '42P01') {
        return {
          success: false,
          message: 'Rate limiting database tables not found. Please run migrations.',
          timestamp: new Date().toISOString()
        }
      }

      return {
        success: true,
        message: 'Rate limiting is properly configured',
        details: {
          perMinute: rateLimits.perMinute,
          perHour: rateLimits.perHour,
          perDay: rateLimits.perDay,
          burstLimit: rateLimits.burstLimit
        },
        timestamp: new Date().toISOString()
      }
    } catch (dbError) {
      return {
        success: false,
        message: 'Rate limiting database check failed',
        details: dbError,
        timestamp: new Date().toISOString()
      }
    }
  } catch (error) {
    return {
      success: false,
      message: `Rate limiting test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Generate recommendations based on test results
 */
function generateRecommendations(report: EmailDeliverabilityReport): string[] {
  const recommendations: string[] = []

  if (!report.configurationTest.success) {
    recommendations.push('Fix email configuration issues before proceeding')
  }

  if (!report.templateTest.success) {
    recommendations.push('Review and fix email template rendering issues')
  }

  if (!report.deliveryTest.success) {
    recommendations.push('Check email provider credentials and network connectivity')
  }

  if (!report.webhookTest.success) {
    recommendations.push('Configure webhook secret for bounce handling')
  }

  if (!report.rateLimitTest.success) {
    recommendations.push('Run database migrations to set up rate limiting')
  }

  if (report.overall.score < 100 && report.overall.score >= 80) {
    recommendations.push('Email system is mostly functional but has minor issues')
  }

  if (report.overall.score < 80) {
    recommendations.push('Email system has significant issues that need attention')
  }

  if (recommendations.length === 0) {
    recommendations.push('Email system is fully functional and ready for production')
  }

  return recommendations
}

/**
 * Quick email test function for development
 */
export async function quickEmailTest(testEmail: string): Promise<boolean> {
  try {
    const response = await sendEmail({
      type: 'test',
      to: testEmail,
      data: {
        test_message: 'Quick email test',
        timestamp: new Date().toISOString()
      }
    })

    console.log('Quick email test result:', response)
    return response.success
  } catch (error) {
    console.error('Quick email test failed:', error)
    return false
  }
}
